package org.example;

import cn.hutool.core.io.FileUtil;

import java.io.File;
import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @program: test
 * @description:
 * @author: luoting
 * @create: 2023-08-16 10:39
 **/
public class Test0816 {


    public static void main(String[] args) throws IOException {

        List<File> loopFiles = FileUtil.loopFiles("C:\\Users\\<USER>\\Desktop\\APP9218_ENV1_VERSION1_0_5_2025-05-21 17_13_56");
        AtomicInteger count = new AtomicInteger(0);
        loopFiles.forEach(t -> {
            if(t.getName().contains("disconf")){
                return;
            }
            List<String> stringList = FileUtil.readLines(t, "UTF-8");
            List<String> newList = new ArrayList<>();
            stringList.forEach(str -> {
                if (str.contains("db_bosshis_url")) {
                    if (count.get() < 6) {
                        str = "db_bosshis_url:*******************************************************,wmzwhdbp2.boss.js.cmcc:2883/bosshis?continueBatchOnError=false&allowMultiQueries=true&rewriteBatchedStatements=true&loadBalanceStrategy=RANDOM";

                    }else {

                        str = "db_bosshis_url:*******************************************************,wmzwhdbp1.boss.js.cmcc:2883/bosshis?continueBatchOnError=false&allowMultiQueries=true&rewriteBatchedStatements=true&loadBalanceStrategy=RANDOM";

                    }
                    count.incrementAndGet();
                }
                if (str.contains("db_bosshis_username")) {
                    str = "db_bosshis_username:bosshis@bosshdb#hisdb";
                }
                if (str.contains("db_bosshis_driver")) {
                    str = "db_bosshis_driver:com.oceanbase.jdbc.Driver";
                }
                newList.add(str);

            });
            FileUtil.writeLines(newList,t,"UTF-8");

        });

    }


}


        /*try (DirectoryStream<Path> stream = Files.newDirectoryStream(dir, "*.{sql}")) {
            for (Path file : stream) {
                System.out.println(file.getFileName());
                // Step 2: For each file in the list, read the contents of the file
                String contents = new String(Files.readAllBytes(file));

                // Step 3: Search for the specific string '||'&'||'&'||'&' in the file contents
                if (contents.contains("'||'&'||'amp;")) {
                    System.out.println("ssssssssssssssss");
                    String replaceStr = "";
                    // Step 4: Replace the found string with '||CHR(38)||''||CHR(38)||'
                    contents = contents.replace("'||'&'||'amp;","'||CHR(38)||'");

                    // Step 5: Write the modified contents of the file back to the same file
                    Files.write(file, contents.getBytes());
                }
            }
        } catch (
                IOException e) {
            e.printStackTrace();
        }*/










