package org.example;

import cn.hutool.core.io.FileUtil;

import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * @program: test001
 * @description:
 * @author: luoting
 * @create: 2025-05-16 10:06
 **/
public class Test0516 {

    public static void main(String[] args) {
        Path path = Paths.get("C:\\Users\\<USER>\\Desktop\\新配置\\sql_脚本");
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(path, "*.{sql}")) {
            for (Path file : stream) {
               /* System.out.println(file.getFileName());*/
                // Step 2: For each file in the list, read the contents of the file
                String contents = new String(Files.readAllBytes(file));

                // Step 3: Search for the specific string '||'&'||'&'||'&' in the file contents
                if (contents.contains("'||'&'||'amp;")) {
                    System.out.println(file.getFileName());
                    System.out.println("ssssssssssssssss");
                    // Step 4: Replace the found string with '||CHR(38)||''||CHR(38)||'
                    contents = contents.replace("'||'&'||'amp;","'||CHR(38)||'");
                    System.out.println(contents);
                    // Step 5: Write the modified contents of the file back to the same file
                   Files.write(file, contents.getBytes());
                }
            }
        }  catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
