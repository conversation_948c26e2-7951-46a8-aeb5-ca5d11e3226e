package org.example;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @program: test001
 * @description:
 * @author: luoting
 * @create: 2025-06-05 19:09
 **/
public class Test0605 {

    public static void main(String[] args) {


        boolean flag = getMonFlag();
        System.out.println("flag....."+flag);


    }

    private static boolean getMonFlag() {
        List<String> resourceList = new ArrayList<>(Arrays.asList(
                "resource-user_cumulate_disct_info",
                "resource-user_cumulate_other_info",
                "resource-user_cumulate_cross_info"));


        for (int i = 0; i < resourceList.size(); i++) {
            if (i == 2) {
                return false;
            }
        }

        return true;
    }

    public static int get_monthnumber4(int yearMonth) {
        return ((int) (yearMonth / 100) * 12 + yearMonth % 100 + 1) % 4 + 1;
    }
}
