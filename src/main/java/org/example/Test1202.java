package org.example;

/**
 * @program: test
 * @description:
 * @author: luoting
 * @create: 2024-12-02 11:36
 **/
public class Test1202 {
    public static void main(String[] args) {


        System.out.println(Long.parseLong("202411")/100);
 /*       String str = "VIP_SATURN_ZK_CONNECTION={VIP_SATURN_ZK_CONNECTION}\n" +
                "APP_NAME={DISCONF_APP}\n" +
                "DISCONF_APP={DISCONF_APP}\n" +
                "SATURN_NAMESPACE={SATURN_NAMESPACE}\n" +
                "LOG_BOOTSTRAP={LOG_BOOTSTRAP}\n" +
                "LOG_TOPIC=stream_billing_saturn\n" +
                "DB_PD_PATH=/usr/local/saturn-executor/conf/.boss_auth_newfile.cfg";
        Map<String, String> map = new HashMap<>();
    *//*    map.put("9936", "fixedfeedetect_ZJ");
        map.put("9937", "fixedfeedetect_HA");
        map.put("9938", "fixedfeedetect_WX");
        map.put("9939", "fixedfeedetect_CZ");
        map.put("9940", "fixedfeedetect_YZ");
        map.put("9941", "fixedfeedetect_NT");
        map.put("9942", "fixedfeedetect_TZ");
        map.put("9943", "fixedfeedetect_YC");
        map.put("9944", "fixedfeedetect_NJ");
        map.put("9945", "fixedfeedetect_SQ");
        map.put("9946", "fixedfeedetect_XZ");
        map.put("9947", "fixedfeedetect_LYG");
        map.put("9948", "fixedfeedetect_SZ");*//*


        map.put("9843","repeatDistributed_SZ");
        map.put("9844","repeatDistributed_HA");
        map.put("9845","repeatDistributed_SQ");
        map.put("9846","repeatDistributed_NJ");
        map.put("9847","repeatDistributed_LYG");
        map.put("9848","repeatDistributed_XZ");
        map.put("9849","repeatDistributed_CZ");
        map.put("9850","repeatDistributed_ZJ");
        map.put("9851","repeatDistributed_WX");
        map.put("9852","repeatDistributed_NT");
        map.put("9853","repeatDistributed_TZ");
        map.put("9854","repeatDistributed_YC");
        map.put("9855","repeatDistributed_YZ");

        Map<String, String> kafkaMap = new HashMap<>();
        kafkaMap.put("JP", "10.32.211.22:42624,10.32.211.21:33588,10.32.210.76:56446,10.32.211.33:41104,10.32.211.26:43456");
        kafkaMap.put("WX", "10.33.231.71:9092,10.33.231.72:9092,10.33.231.73:9092,10.33.231.74:9092,10.33.231.75:9092");
        List<String> db3 = Arrays.asList("SQ", "LYG", "XZ", "NJ");
        Map<String, String> zkmap = new HashMap<>();


        zkmap.put("yz", "10.33.235.39:47637,10.33.235.35:47637,10.33.235.32:47637,10.33.235.37:47637,10.33.235.38:47637");
        zkmap.put("yc", "10.33.235.39:56115,10.33.235.35:56115,10.33.235.32:56115,10.33.235.37:56115,10.33.235.38:56115");
        zkmap.put("tz", "10.33.235.39:40146,10.33.235.38:40146,10.33.235.33:40146,10.33.235.34:40146,************:40146");
        zkmap.put("nt", "10.33.235.39:30488,10.33.235.35:30488,10.33.235.32:30488,10.33.235.37:30488,10.33.235.38:30488");

        zkmap.put("wx", "10.33.235.35:42007,10.33.235.32:42007,10.33.235.33:42007,10.33.235.34:42007,************:42007");
        zkmap.put("ha", "10.33.235.35:56452,10.33.235.32:56452,10.33.235.33:56452,10.33.235.34:56452,************:56452");
        zkmap.put("zj", "10.33.235.35:51746,10.33.235.32:51746,10.33.235.33:51746,10.33.235.34:51746,************:51746");
        zkmap.put("cz", "10.33.235.35:42318,10.33.235.32:42318,10.33.235.33:42318,10.33.235.34:42318,************:42318");

        zkmap.put("xz", "10.32.211.28:40170,10.32.211.23:40170,10.32.211.30:40170,10.32.211.29:40170,10.32.211.25:40170");
        zkmap.put("ly", "0.32.211.28:58121,10.32.211.23:58121,10.32.211.30:58121,10.32.211.29:58121,10.32.211.25:58121");
        zkmap.put("nj", "10.32.211.28:55959,10.32.211.23:55959,10.32.211.30:55959,10.32.211.29:55959,10.32.211.25:55959");
        zkmap.put("sq", "10.32.211.28:52182,10.32.211.23:52182,10.32.211.30:52182,10.32.211.29:52182,10.32.211.25:52182");

        zkmap.put("sz", "10.33.235.35:30023,10.33.235.32:30023,10.33.235.33:30023,10.33.235.34:30023,************:30023");


        map.forEach((k, v) -> {
            String filename = "C:\\Users\\<USER>\\Desktop\\去挂载脚本\\1127rar\\repeatDis\\" + "APP" + k + "_rep" + "\\process-env.properties";

            Map<String, String> conmap = new HashMap<>();

            conmap.put("DISCONF_APP", v);
            conmap.put("SATURN_NAMESPACE", v.toLowerCase(Locale.ROOT));
            conmap.put("LOG_BOOTSTRAP", "");
            String CITYNAME = v.split("_")[1];
            conmap.put("VIP_SATURN_ZK_CONNECTION", zkmap.get(CITYNAME.toLowerCase()));


            if (db3.contains(CITYNAME)) {
                conmap.put("LOG_BOOTSTRAP", kafkaMap.get("JP"));
            } else {
                conmap.put("LOG_BOOTSTRAP", kafkaMap.get("WX"));

            }
            String con = StrUtil.format(str, conmap);
            System.out.println(conmap);
            FileUtil.writeString(con, new File(filename), "UTF-8");
        });
*/

    }
}
