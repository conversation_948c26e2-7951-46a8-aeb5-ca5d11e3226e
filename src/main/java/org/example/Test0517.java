package org.example;

import cn.hutool.core.io.FileUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;

import java.util.List;
import java.util.Map;

/**
 * @program: test001
 * @description:
 * @author: luoting
 * @create: 2025-05-17 18:05
 **/
public class Test0517 {

    public static void main(String[] args) {
        ExcelReader reader = ExcelUtil.getReader("C:\\Users\\<USER>\\Desktop\\新建文件夹\\表数据.xls");
        List<Map<String, Object>> maps = reader.readAll();
        maps.forEach(line->{
           line.forEach((k,v)->
           {

              /* if(k.equals("INOUT_TYPE") && )*/

               System.out.println("k="+k+",v="+v);
           });
        });



    }
}
