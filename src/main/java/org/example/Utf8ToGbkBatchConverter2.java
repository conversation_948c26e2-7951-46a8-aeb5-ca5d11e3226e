package org.example;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class Utf8ToGbkBatchConverter2 {

    public static void main(String[] args) throws IOException {
        // 修改为你的文件夹路径
        ExcelReader excelReader = ExcelUtil.getReader("C:\\Users\\<USER>\\Desktop\\app.xlsx");
        List<Map<String, Object>> mapList = excelReader.readAll();
        List<String> str = new ArrayList<>();
        mapList.forEach(lineMap->{
            lineMap.forEach((k,v)->{
                if(k.equals("应用标识")){
                    str.add(v.toString());
                }
            });
        });

        List<List<String>> split = CollUtil.split(str, 50);
        split.forEach(a->{
            System.out.println("app_codes_wx = ["+"]");
            System.out.println("app_codes_yz = ["+ StrUtil.join(",",a) +"]");
            System.out.println("......................................................................");
        });


    }
}
