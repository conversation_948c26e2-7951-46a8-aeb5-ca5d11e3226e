package org.example;

import cn.hutool.core.io.FileUtil;

import java.io.File;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: test001
 * @description:
 * @author: luoting
 * @create: 2025-05-14 15:44
 **/
public class Test0514 {
    public static void main(String[] args) {
        List<File> loopFiles = FileUtil.loopFiles("C:\\Users\\<USER>\\Desktop\\新配置");

        loopFiles.forEach(file -> {
            if (file.getName().equals("common.properties") || file.getName().equals("common_base.properties")) {
                FileUtil.del(file);
            }

            if (file.getName().equals("log4j2.xml")) {
                String string = FileUtil.readString(file, "UTF-8");
                String noComments = string.replaceAll("(?s)<!--.*?-->", "");
                FileUtil.writeString(noComments, file, "UTF-8");
            }

            if (file.getName().equals("application.yml")) {
                String string = FileUtil.readString(file, "UTF-8");
                String strNew = string.replaceAll("disconfweb:48081", "disconfweb:52005");
                FileUtil.writeString(strNew, file, "UTF-8");
            }
            if (file.getName().equals("process-env.properties")) {
                List<String> strings = FileUtil.readLines(file, "UTF-8");
                List<String> stringNew = new ArrayList<>();
                strings.forEach(str->{
                    if(str.contains("LOG_BOOTSTRAP")){
                        str="LOG_BOOTSTRAP=*************:30311,************:30312,*************:30313,*************:30314,*************:30315,*************:30316";
                    }

                    if(str.contains("VIP_SATURN_ZK_CONNECTION")){
                        str="VIP_SATURN_ZK_CONNECTION=************:30113,************:30113,*************:30113,*************:30113,*************:30113";
                    }


                    if(str.contains("HNAME")){
                        str="HNAME=nclyizn";
                    }


                    if(str.contains("DISCONF_APP_VERSION")){
                        str="DISCONF_APP_VERSION=1_0_5";
                    }

                    if(str.contains("DISCONF_COMMON_VERSION")){
                        str="DISCONF_COMMON_VERSION=1_0_5";
                    }
                    stringNew.add(str);
                });

                FileUtil.writeLines(stringNew, file, "UTF-8");
            }

            if (file.getName().equals("ignite-spring.properties")) {
                List<String> strings = FileUtil.readLines(file, "UTF-8");
                List<String> stringNew = new ArrayList<>();
                strings.forEach(str->{
                    if(str.contains("ignite.client.address")){
                        str = "ignite.client.address=*************:13301..13306,*************:13301..13306,*************:13301..13306,************:13301..13306,*************:13301..13306,*************:13301..13306";
                    }
                    if(str.contains("ignite.client.tcpAddress")){
                        str = "ignite.client.tcpAddress=*************:13101..13104,*************:13101..13104,*************:13101..13104,************:13101..13104,*************:13101..13104,*************:13101..13104";
                    }
                    stringNew.add(str);
                });
                FileUtil.writeLines(stringNew, file, "UTF-8");

            }
        });

    }

}
