package org.example;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Hello world!
 *
 */
public class App 
{
    public static void main( String[] args ) throws Exception {
        List<File> loopFiles = FileUtil.loopFiles("C:\\Users\\<USER>\\Desktop\\0515");
        // 指定目录
        //File parentDir = new File("C:\\\\Users\\\\<USER>\\\\Desktop\\\\0515");

        File parentDir = new File("C:\\Users\\<USER>\\Desktop\\新配置"); // 例如 "/tmp/test"
        if (parentDir.isDirectory()) {
            File[] subFiles = parentDir.listFiles();
            if (subFiles != null) {
                for (File file : subFiles) {
                    if (file.isDirectory()) {
                        System.out.println(file.getName().substring(3,8));
                    }
                }
            }
        }
    }

    public static List<String> getContentList(String fileName, int isUtf8) throws Exception {
        List<String> resultList = new ArrayList<>();
        if(StrUtil.isNotEmpty(fileName)){
            try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(new FileInputStream(new File(fileName)), isUtf8==1 ? "UTF-8" : "gbk"))){
                String line = null;
                if(null!=bufferedReader){
                    while(null != (line = bufferedReader.readLine())){
                        resultList .add(line);
                    }
                }
            }catch (Exception e){

                e.printStackTrace();
            }
        }


        return resultList;
    }
    public static String subStrByBytesIndex(String str, int begin, int end, String codingType) throws UnsupportedEncodingException
    {
        if (StrUtil.isNotEmpty(str))
        {
            byte[] bytes = str.getBytes(codingType);
            byte[] newBytes = new byte[end - begin];
            for (int i = begin, j = 0; i < end; i++, j++)
            {
                newBytes[j] = bytes[i];
            }
            String string = new String(newBytes, codingType);

            return string;
        }
        else
        {

        }
        return null;
    }
}
