package org.example;

import cn.hutool.core.io.FileUtil;

import java.io.*;
import java.util.List;

public class Utf8GarbledDetector {
    // 判断一个字符是否可能是中文（也可根据需要放宽范围）
    private static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS ||
                ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS ||
                ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A ||
                ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B ||
                ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION ||
                ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS ||
                ub == Character.UnicodeBlock.GENERAL_PUNCTUATION;
    }

    private static boolean isMessyCode(String str) {
        int chnChars = 0, messyChars = 0;
        char[] arr = str.toCharArray();
        for (char c : arr) {
            // 过滤常见的ASCII、中文字符
            if (!Character.isLetterOrDigit(c) && !isChinese(c) && c != ' ' && c != '.' && c != ',') {
                messyChars++;
            } else if (isChinese(c)) {
                chnChars++;
            }
        }
        float ratio = messyChars * 1.0f / (chnChars + 1); // 防止0除
        return ratio > 0.4; // 超过一定比例认为有乱码
    }

    public static void main(String[] args) throws IOException {
        List<File> fileList = FileUtil.loopFiles("C:\\Users\\<USER>\\Desktop\\脚本");

        fileList.forEach(file -> {
            String filePath = file.getAbsolutePath();
            BufferedReader reader = null;
            try {
                reader = new BufferedReader(new InputStreamReader(new FileInputStream(filePath), "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            } catch (FileNotFoundException e) {
                throw new RuntimeException(e);
            }
            String line;
            int lineNum = 0;
            boolean found = false;
            while (true) {
                try {
                    if (!((line = reader.readLine()) != null)) break;
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                lineNum++;
                if (line.contains("�")) {
                    //System.out.println("第" + lineNum + "行可能存在乱码：[" + line + "]");
                    found = true;
                }
            }
            if (!found) {
                System.out.println("未发现明显乱码");
            }else {
                System.out.println(file.getName());
            }
            try {
                reader.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });

    }
}
