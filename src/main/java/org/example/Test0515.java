package org.example;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: test001
 * @description:
 * @author: luoting
 * @create: 2025-05-15 19:06
 **/
public class Test0515 {

    public static void main(String[] args) {
        //List<File> loopFiles = FileUtil.loopFiles("C:\\Users\\<USER>\\Desktop\\0515");
        // 指定目录
        //File parentDir = new File("C:\\\\Users\\\\<USER>\\\\Desktop\\\\0515");

        /*File parentDir = new File("C:\\Users\\<USER>\\Desktop\\0515"); // 例如 "/tmp/test"
        if (parentDir.isDirectory()) {
            File[] subFiles = parentDir.listFiles();
            if (subFiles != null) {
                for (File file : subFiles) {
                    if (file.isDirectory()) {
                        System.out.println(file.getName().substring(3,8));
                    }
                }
            }
        }*/
        String str = "40949";
String sql = "insert into disconf_config (config_id,type,NAME,value,app_id,version,env_id,create_time,update_time,status,log_version,config_group,app_name,is_autodownload,Md5_Value,choice_type,ref_id) \n" +
        "values (\n" +
        "CONFIG_CONFIG_ID_SEQ.nextval,0,(select NAME from disconf_config where config_id = (select config_id from disconf_config where app_id = 9225 and version = '1_0_5' and name = 'common_base.properties')),' ',\n" +
        "{appId},\n" +
        "'1_0_5',1,to_char(sysdate,'yyyy-MM-dd HH:mi:ss'),to_char(sysdate,'yyyy-MM-dd HH:mi:ss'),1,1,'common',\n" +
        "(select name from disconf_app where app_id = {appId}),\n" +
        "1,(select md5_value from disconf_config where config_id = (select config_id from disconf_config where app_id = 9225 and version = '1_0_5' and name = 'common_base.properties')),3,(select config_id from disconf_config where app_id = 9225 and version = '1_0_5' and name = 'common_base.properties'));\n" +
        "\n" +
        "insert into disconf_config (config_id,type,NAME,value,app_id,version,env_id,create_time,update_time,status,log_version,config_group,app_name,is_autodownload,Md5_Value,choice_type,ref_id) \n" +
        "values (\n" +
        "CONFIG_CONFIG_ID_SEQ.nextval,0,(select NAME from disconf_config where config_id = (select config_id from disconf_config where app_id = 9225 and version = '1_0_5' and name = 'common.properties')),' ',\n" +
        "{appId},\n" +
        "'1_0_5',1,to_char(sysdate,'yyyy-MM-dd HH:mi:ss'),to_char(sysdate,'yyyy-MM-dd HH:mi:ss'),1,1,'common',\n" +
        "(select name from disconf_app where app_id = {appId}),\n" +
        "1,(select md5_value from disconf_config where config_id = (select config_id from disconf_config where app_id = 9225 and version = '1_0_5' and name = 'common.properties')),3,(select config_id from disconf_config where app_id = 9225 and version = '1_0_5' and name = 'common.properties'));";
        String[] split = str.split(",");


        List<String> list = new ArrayList<>();
        for (int i = 0; i < split.length; i++) {
            Map<String,String> map = new HashMap<>();
            map.put("appId",split[i]);
            String format = StrUtil.format(sql, map);
            list.add(format);

        }
        FileUtil.writeLines(list,"C:\\Users\\<USER>\\Desktop\\新配置\\LUOTING.SQL","GB2312");
        
    }
}
