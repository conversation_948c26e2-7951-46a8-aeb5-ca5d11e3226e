package org.example;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @program: test001
 * @description:
 * @author: luoting
 * @create: 2025-07-03 16:16
 **/
public class Test0703 {

    public static void main(String[] args) {
        String str = "C:\\Users\\<USER>\\Desktop\\cmp\\t_cmp_appcode.xlsx";
        String str1 = "C:\\Users\\<USER>\\Desktop\\cmp\\boss.xlsx";

        ExcelReader reader = ExcelUtil.getReader(str);
        ExcelReader reader1 = ExcelUtil.getReader(str1);
        List<Map<String, Object>> readAll = reader.readAll();
        List<Map<String, Object>> readAll1 = reader1.readAll();
        List<Map<String,String>> newList = new ArrayList<>();
        readAll.forEach(t->{
            t.forEach((k,v)->{
                if(k.equals("app_name")){
                    String appname = v.toString();

                    System.out.println("appname="+appname);
                }

            });

        });


        readAll1.forEach(r1->{
            r1.forEach((k1,v1)->{

                if(k1.equals("应用名") && v1.equals(appname)){

                }
            });
        });
        ExcelWriter excelWriter = new ExcelWriter("C:\\Users\\<USER>\\Desktop\\appcode.xlsx");
        excelWriter.write(newList);
        excelWriter.close();
        System.out.println(newList);
    }
}
